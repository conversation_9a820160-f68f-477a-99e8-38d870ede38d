using System;
using System.IO;
using System.Windows.Forms;
using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli
{
    /// <summary>
    /// 页面设置配置测试类
    /// </summary>
    public static class PageSetupConfigTest
    {
        /// <summary>
        /// 测试页面设置保存和加载功能
        /// </summary>
        public static void TestPageSetupSaveAndLoad()
        {
            try
            {
                Console.WriteLine("开始测试页面设置保存和加载功能...");

                // 1. 创建测试设置
                var testSettings = new PageSetupSettings
                {
                    Width = 30.0f,
                    Height = 20.0f,
                    Unit = "厘米",
                    IsLandscape = false,
                    SizeType = "Custom",
                    MaintainAspectRatio = true,
                    BackgroundSettings = new BackgroundSettings
                    {
                        BackgroundType = "SolidColor",
                        BackgroundColor = "#FF0000"
                    }
                };

                Console.WriteLine($"创建测试设置: {testSettings.Width}x{testSettings.Height} {testSettings.Unit}");

                // 2. 保存设置
                ConfigService.Instance.SavePageSetupSettings(testSettings);
                Console.WriteLine("设置已保存到配置文件");

                // 3. 清除缓存，强制从文件重新加载
                ClearPageSetupCache();
                Console.WriteLine("已清除缓存");

                // 4. 重新加载设置
                var loadedSettings = ConfigService.Instance.GetPageSetupSettings();
                Console.WriteLine($"重新加载的设置: {loadedSettings.Width}x{loadedSettings.Height} {loadedSettings.Unit}");

                // 5. 验证设置是否正确保存和加载
                bool isCorrect = 
                    Math.Abs(loadedSettings.Width - testSettings.Width) < 0.01f &&
                    Math.Abs(loadedSettings.Height - testSettings.Height) < 0.01f &&
                    loadedSettings.Unit == testSettings.Unit &&
                    loadedSettings.IsLandscape == testSettings.IsLandscape &&
                    loadedSettings.SizeType == testSettings.SizeType &&
                    loadedSettings.MaintainAspectRatio == testSettings.MaintainAspectRatio;

                if (isCorrect)
                {
                    Console.WriteLine("✓ 测试通过：页面设置保存和加载功能正常");
                }
                else
                {
                    Console.WriteLine("✗ 测试失败：页面设置保存和加载功能异常");
                    Console.WriteLine($"期望: {testSettings.Width}x{testSettings.Height} {testSettings.Unit}");
                    Console.WriteLine($"实际: {loadedSettings.Width}x{loadedSettings.Height} {loadedSettings.Unit}");
                }

                // 6. 检查配置文件是否存在
                string configPath = Path.Combine(Application.StartupPath, "Config", "PageSetupConfig.json");
                if (File.Exists(configPath))
                {
                    Console.WriteLine($"✓ 配置文件存在: {configPath}");
                    string content = File.ReadAllText(configPath);
                    Console.WriteLine($"配置文件内容长度: {content.Length} 字符");
                }
                else
                {
                    Console.WriteLine($"✗ 配置文件不存在: {configPath}");
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 清除页面设置缓存（通过反射）
        /// </summary>
        private static void ClearPageSetupCache()
        {
            try
            {
                var configService = ConfigService.Instance;
                var type = configService.GetType();
                
                // 清除页面设置缓存
                var pageSetupField = type.GetField("_pageSetupSettings", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (pageSetupField != null)
                {
                    pageSetupField.SetValue(configService, null);
                }

                // 清除完整配置缓存
                var currentConfigField = type.GetField("_currentConfig", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (currentConfigField != null)
                {
                    currentConfigField.SetValue(configService, null);
                }

                Console.WriteLine("缓存清除成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清除缓存时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试页面设置窗口的保存功能
        /// </summary>
        public static void TestPageSetupFormSave()
        {
            try
            {
                Console.WriteLine("开始测试页面设置窗口保存功能...");

                // 创建并显示页面设置窗口
                using var pageSetupForm = new Forms.PageSetupForm();
                
                // 模拟用户修改设置（这里只能通过反射或公共方法来设置）
                Console.WriteLine("页面设置窗口已创建");
                
                // 获取当前设置
                var currentSettings = pageSetupForm.GetCurrentSettings();
                Console.WriteLine($"当前设置: {currentSettings.Width}x{currentSettings.Height} {currentSettings.Unit}");

                Console.WriteLine("页面设置窗口测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 页面设置窗口测试失败: {ex.Message}");
            }
        }
    }
}
