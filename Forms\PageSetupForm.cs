// PPT页面设置窗体
// 功能：设置PPT页面尺寸、方向、背景等属性，支持预设尺寸和自定义尺寸
// 作者：PPT批量处理工具开发团队
// 创建时间：2024年
// 最后修改：2024年

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 页面设置窗体
    /// </summary>
    public partial class PageSetupForm : Form
    {
        /// <summary>
        /// 预设尺寸信息
        /// </summary>
        private readonly Dictionary<string, SizeF> _presetSizes;

        /// <summary>
        /// 单位转换比例（相对于毫米）
        /// </summary>
        private readonly Dictionary<string, float> _unitConversions;

        /// <summary>
        /// 当前设置
        /// </summary>
        private PageSetupSettings _currentSettings;

        /// <summary>
        /// 背景设置
        /// </summary>
        private BackgroundSettings _backgroundSettings;

        /// <summary>
        /// 是否正在更新控件值（防止递归调用）
        /// </summary>
        private bool _isUpdating = false;

        public PageSetupForm()
        {
            InitializeComponent();

            // 初始化预设尺寸
            _presetSizes = new Dictionary<string, SizeF>
            {
                { "标准 4:3 (25.4 x 19.05 cm)", new SizeF(254f, 190.5f) },
                { "宽屏 16:9 (25.4 x 14.29 cm)", new SizeF(254f, 142.9f) },
                { "宽屏 16:10 (25.4 x 15.88 cm)", new SizeF(254f, 158.8f) },
                { "A4 纸张 (21.0 x 29.7 cm)", new SizeF(210f, 297f) },
                { "A3 纸张 (29.7 x 42.0 cm)", new SizeF(297f, 420f) },
                { "Letter (21.59 x 27.94 cm)", new SizeF(215.9f, 279.4f) },
                { "Legal (21.59 x 35.56 cm)", new SizeF(215.9f, 355.6f) },
                { "35mm 幻灯片 (3.6 x 2.4 cm)", new SizeF(36f, 24f) }
            };

            // 初始化单位转换
            _unitConversions = new Dictionary<string, float>
            {
                { "毫米", 1f },
                { "厘米", 10f },
                { "英寸", 25.4f },
                { "磅", 0.352778f },
                { "像素", 0.264583f } // 96 DPI
            };

            // 初始化当前设置
            _currentSettings = new PageSetupSettings();
            _backgroundSettings = new BackgroundSettings();

            InitializeControls();
            LoadCurrentSettings();
            SetupEventHandlers();

            // 设置窗体关闭事件 - 确保直接关闭窗口时自动保存配置
            FormClosing += PageSetupForm_FormClosing;
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            // 初始化预设尺寸下拉框
            comboPresetSizes.Items.Clear();
            foreach (var preset in _presetSizes.Keys)
            {
                comboPresetSizes.Items.Add(preset);
            }
            comboPresetSizes.SelectedIndex = 0; // 默认选择标准4:3

            // 初始化宽度单位下拉框
            comboWidthUnit.Items.Clear();
            foreach (var unit in _unitConversions.Keys)
            {
                comboWidthUnit.Items.Add(unit);
            }
            comboWidthUnit.SelectedIndex = 1; // 默认选择厘米

            // 初始化高度单位下拉框
            comboHeightUnit.Items.Clear();
            foreach (var unit in _unitConversions.Keys)
            {
                comboHeightUnit.Items.Add(unit);
            }
            comboHeightUnit.SelectedIndex = 1; // 默认选择厘米

            // 设置数值框的初始值
            numWidth.Value = 25.4m;
            numHeight.Value = 19.05m;

            // 初始化背景设置控件
            InitializeBackgroundControls();

            // 设置控件文字居中
            SetControlsTextAlignment();
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 预设尺寸选择事件 - 用于快速选择常用的幻灯片尺寸规格
            comboPresetSizes.SelectedIndexChanged += ComboPresetSizes_SelectedIndexChanged;

            // 尺寸类型单选按钮事件 - 控制幻灯片的基本比例类型
            // radioStandard: 启用标准4:3比例，适用于传统演示文稿
            // radioWidescreen: 启用宽屏16:9比例，适用于现代显示器
            // radioCustom: 启用自定义尺寸，允许用户自由设置宽高
            radioStandard.CheckedChanged += RadioSizeType_CheckedChanged;
            radioWidescreen.CheckedChanged += RadioSizeType_CheckedChanged;
            radioCustom.CheckedChanged += RadioSizeType_CheckedChanged;

            // 数值变更事件 - 监控宽度和高度数值的实时变化
            numWidth.ValueChanged += NumSize_ValueChanged;
            numHeight.ValueChanged += NumSize_ValueChanged;

            // 单位变更事件 - 处理不同测量单位之间的转换
            comboWidthUnit.SelectedIndexChanged += ComboWidthUnit_SelectedIndexChanged;
            comboHeightUnit.SelectedIndexChanged += ComboHeightUnit_SelectedIndexChanged;

            // 方向选择事件 - 控制幻灯片的显示方向
            // radioLandscape: 启用横向模式，宽度大于高度，适合演示文稿
            // radioPortrait: 启用纵向模式，高度大于宽度，适合文档和报告
            radioLandscape.CheckedChanged += RadioOrientation_CheckedChanged;
            radioPortrait.CheckedChanged += RadioOrientation_CheckedChanged;

            // 比例相关事件
            // chkMaintainRatio: 启用宽高比锁定，修改一个数值时自动调整另一个数值保持比例
            chkMaintainRatio.CheckedChanged += ChkMaintainRatio_CheckedChanged;

            // 背景设置事件 - 控制幻灯片背景的显示方式
            // radioNoBackground: 启用无背景模式，使用默认白色背景
            // radioSolidColor: 启用纯色背景，可选择单一颜色作为背景
            // radioGradient: 启用渐变背景，支持两种颜色的渐变效果
            // radioImage: 启用图片背景，可选择图片文件作为背景
            radioNoBackground.CheckedChanged += RadioBackground_CheckedChanged;
            radioSolidColor.CheckedChanged += RadioBackground_CheckedChanged;
            radioGradient.CheckedChanged += RadioBackground_CheckedChanged;
            radioImage.CheckedChanged += RadioBackground_CheckedChanged;

            // 图片填充模式变更事件 - 控制图片在背景中的显示方式
            comboImageFillMode.SelectedIndexChanged += ComboImageFillMode_SelectedIndexChanged;
        }

        /// <summary>
        /// 加载当前设置
        /// </summary>
        private void LoadCurrentSettings()
        {
            try
            {
                // 从配置服务加载设置
                var config = ConfigService.Instance.GetConfig();
                if (config.PageSetupSettings != null)
                {
                    _currentSettings = config.PageSetupSettings;

                    // 同步背景设置
                    if (_currentSettings.BackgroundSettings != null)
                    {
                        // 深拷贝背景设置，避免引用问题
                        _backgroundSettings = new BackgroundSettings
                        {
                            BackgroundType = _currentSettings.BackgroundSettings.BackgroundType,
                            BackgroundColor = _currentSettings.BackgroundSettings.BackgroundColor,
                            GradientStartColor = _currentSettings.BackgroundSettings.GradientStartColor,
                            GradientEndColor = _currentSettings.BackgroundSettings.GradientEndColor,
                            GradientDirection = _currentSettings.BackgroundSettings.GradientDirection,
                            ImagePath = _currentSettings.BackgroundSettings.ImagePath,
                            ImageFillMode = _currentSettings.BackgroundSettings.ImageFillMode,
                            ImageTransparency = _currentSettings.BackgroundSettings.ImageTransparency
                        };
                    }
                    else
                    {
                        _backgroundSettings = new BackgroundSettings();
                    }

                    // 记录加载的设置信息
                    LogService.Instance.LogConfigChange($"页面设置已加载: {_currentSettings.Width}x{_currentSettings.Height} {_currentSettings.Unit}, 方向: {(_currentSettings.IsLandscape ? "横向" : "纵向")}, 类型: {_currentSettings.SizeType}");

                    ApplySettingsToControls();
                }
                else
                {
                    LogService.Instance.LogProcessError("页面设置为空，使用默认设置", null);
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"加载页面设置失败: {ex.Message}", ex);
                MessageBox.Show($"加载页面设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 将设置应用到控件
        /// </summary>
        private void ApplySettingsToControls()
        {
            _isUpdating = true;
            try
            {
                // 设置尺寸
                numWidth.Value = (decimal)_currentSettings.Width;
                numHeight.Value = (decimal)_currentSettings.Height;

                // 设置单位
                if (comboWidthUnit.Items.Contains(_currentSettings.Unit))
                {
                    comboWidthUnit.SelectedItem = _currentSettings.Unit;
                    comboHeightUnit.SelectedItem = _currentSettings.Unit;
                }

                // 设置方向
                radioLandscape.Checked = _currentSettings.IsLandscape;
                radioPortrait.Checked = !_currentSettings.IsLandscape;

                // 设置比例锁定
                chkMaintainRatio.Checked = _currentSettings.MaintainAspectRatio;

                // 设置复选框状态
                chkEnablePresetSizes.Checked = _currentSettings.EnablePresetSizes;
                chkEnableSizeType.Checked = _currentSettings.EnableSizeType;
                chkEnableCustomSize.Checked = _currentSettings.EnableCustomSize;
                chkEnableAspectRatio.Checked = _currentSettings.EnableAspectRatioAdjustment;

                // 设置尺寸类型
                switch (_currentSettings.SizeType)
                {
                    case "Standard":
                        radioStandard.Checked = true;
                        break;
                    case "Widescreen":
                        radioWidescreen.Checked = true;
                        break;
                    case "Custom":
                        radioCustom.Checked = true;
                        break;
                }

                // 应用背景设置到控件
                ApplyBackgroundSettingsToControls();

                UpdateAspectRatioDisplay();
            }
            finally
            {
                _isUpdating = false;
            }
        }

        /// <summary>
        /// 将背景设置应用到控件
        /// </summary>
        private void ApplyBackgroundSettingsToControls()
        {
            if (_backgroundSettings == null) return;

            try
            {
                // 根据背景类型设置单选按钮
                switch (_backgroundSettings.BackgroundType)
                {
                    case "None":
                        radioNoBackground.Checked = true;
                        break;
                    case "SolidColor":
                        radioSolidColor.Checked = true;
                        // 设置颜色
                        if (!string.IsNullOrEmpty(_backgroundSettings.BackgroundColor))
                        {
                            try
                            {
                                var color = ColorTranslator.FromHtml(_backgroundSettings.BackgroundColor);
                                panelColorPicker.BackColor = color;
                            }
                            catch { /* 忽略颜色解析错误 */ }
                        }
                        break;
                    case "Gradient":
                        radioGradient.Checked = true;
                        // 设置渐变颜色
                        if (!string.IsNullOrEmpty(_backgroundSettings.GradientStartColor))
                        {
                            try
                            {
                                var startColor = ColorTranslator.FromHtml(_backgroundSettings.GradientStartColor);
                                panelGradientStart.BackColor = startColor;
                            }
                            catch { /* 忽略颜色解析错误 */ }
                        }
                        if (!string.IsNullOrEmpty(_backgroundSettings.GradientEndColor))
                        {
                            try
                            {
                                var endColor = ColorTranslator.FromHtml(_backgroundSettings.GradientEndColor);
                                panelGradientEnd.BackColor = endColor;
                            }
                            catch { /* 忽略颜色解析错误 */ }
                        }
                        break;
                    case "Image":
                        radioImage.Checked = true;
                        // 设置图片路径显示
                        if (!string.IsNullOrEmpty(_backgroundSettings.ImagePath))
                        {
                            var fileName = Path.GetFileName(_backgroundSettings.ImagePath);
                            btnSelectImage.Text = $"已选择: {fileName}";
                        }
                        // 设置填充模式
                        if (!string.IsNullOrEmpty(_backgroundSettings.ImageFillMode))
                        {
                            string chineseFillMode = _backgroundSettings.ImageFillMode switch
                            {
                                "Stretch" => "拉伸",
                                "Tile" => "平铺",
                                "Center" => "居中",
                                "Fit" => "适应",
                                "Fill" => "填充",
                                _ => "拉伸"
                            };
                            if (comboImageFillMode.Items.Contains(chineseFillMode))
                            {
                                comboImageFillMode.SelectedItem = chineseFillMode;
                            }
                        }
                        break;
                }

                // 更新控件状态
                UpdateBackgroundControlsState();
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用背景设置到控件时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 预设尺寸选择变更事件
        /// </summary>
        private void ComboPresetSizes_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (_isUpdating || comboPresetSizes.SelectedItem == null) return;

            var selectedPreset = comboPresetSizes.SelectedItem.ToString();
            if (selectedPreset != null && _presetSizes.ContainsKey(selectedPreset))
            {
                var size = _presetSizes[selectedPreset];

                _isUpdating = true;
                try
                {
                    // 转换到当前单位
                    var currentUnit = comboWidthUnit.SelectedItem?.ToString() ?? "厘米";
                    var conversion = _unitConversions[currentUnit];

                    numWidth.Value = (decimal)(size.Width / conversion);
                    numHeight.Value = (decimal)(size.Height / conversion);

                    // 根据预设自动选择尺寸类型
                    if (selectedPreset.Contains("4:3"))
                    {
                        radioStandard.Checked = true;
                    }
                    else if (selectedPreset.Contains("16:9") || selectedPreset.Contains("16:10"))
                    {
                        radioWidescreen.Checked = true;
                    }
                    else
                    {
                        radioCustom.Checked = true;
                    }

                    UpdateAspectRatioDisplay();
                }
                finally
                {
                    _isUpdating = false;
                }
            }
        }

        /// <summary>
        /// 尺寸类型单选按钮变更事件
        /// </summary>
        private void RadioSizeType_CheckedChanged(object? sender, EventArgs e)
        {
            if (_isUpdating) return;

            var radioButton = sender as RadioButton;
            if (radioButton?.Checked == true)
            {
                _isUpdating = true;
                try
                {
                    var currentUnit = comboWidthUnit.SelectedItem?.ToString() ?? "厘米";
                    var conversion = _unitConversions[currentUnit];

                    if (radioButton == radioStandard)
                    {
                        // 设置为标准4:3比例
                        numWidth.Value = (decimal)(254f / conversion);
                        numHeight.Value = (decimal)(190.5f / conversion);
                    }
                    else if (radioButton == radioWidescreen)
                    {
                        // 设置为宽屏16:9比例
                        numWidth.Value = (decimal)(254f / conversion);
                        numHeight.Value = (decimal)(142.9f / conversion);
                    }
                    // radioCustom不自动改变数值

                    UpdateAspectRatioDisplay();
                }
                finally
                {
                    _isUpdating = false;
                }
            }
        }

        /// <summary>
        /// 数值变更事件
        /// </summary>
        private void NumSize_ValueChanged(object? sender, EventArgs e)
        {
            if (_isUpdating) return;

            // 如果锁定比例，自动调整另一个数值
            if (chkMaintainRatio.Checked)
            {
                _isUpdating = true;
                try
                {
                    var aspectRatio = _currentSettings.AspectRatio;
                    if (aspectRatio > 0)
                    {
                        if (sender == numWidth)
                        {
                            numHeight.Value = numWidth.Value / (decimal)aspectRatio;
                        }
                        else if (sender == numHeight)
                        {
                            numWidth.Value = numHeight.Value * (decimal)aspectRatio;
                        }
                    }
                }
                finally
                {
                    _isUpdating = false;
                }
            }

            // 自动选择自定义尺寸
            if (!_isUpdating)
            {
                radioCustom.Checked = true;
            }

            UpdateAspectRatioDisplay();
        }



        /// <summary>
        /// 方向选择变更事件
        /// </summary>
        private void RadioOrientation_CheckedChanged(object? sender, EventArgs e)
        {
            if (_isUpdating) return;

            var radioButton = sender as RadioButton;
            if (radioButton?.Checked == true)
            {
                _isUpdating = true;
                try
                {
                    // 检查是否需要交换宽高
                    var currentWidth = (float)numWidth.Value;
                    var currentHeight = (float)numHeight.Value;

                    if (radioButton == radioPortrait)
                    {
                        // 切换到纵向：确保高度大于宽度
                        if (currentWidth > currentHeight)
                        {
                            numWidth.Value = (decimal)currentHeight;
                            numHeight.Value = (decimal)currentWidth;
                        }
                    }
                    else if (radioButton == radioLandscape)
                    {
                        // 切换到横向：确保宽度大于高度
                        if (currentHeight > currentWidth)
                        {
                            numWidth.Value = (decimal)currentHeight;
                            numHeight.Value = (decimal)currentWidth;
                        }
                    }

                    UpdateAspectRatioDisplay();
                }
                finally
                {
                    _isUpdating = false;
                }
            }
        }



        /// <summary>
        /// 锁定比例复选框变更事件
        /// </summary>
        private void ChkMaintainRatio_CheckedChanged(object? sender, EventArgs e)
        {
            if (chkMaintainRatio.Checked)
            {
                // 锁定时记录当前比例
                _currentSettings.AspectRatio = (float)numWidth.Value / (float)numHeight.Value;
            }
        }



        /// <summary>
        /// 更新宽高比显示
        /// </summary>
        private void UpdateAspectRatioDisplay()
        {
            try
            {
                var width = (float)numWidth.Value;
                var height = (float)numHeight.Value;

                if (height > 0)
                {
                    var ratio = width / height;
                    _currentSettings.AspectRatio = ratio;

                    // 计算最简比例
                    var gcd = CalculateGCD((int)(width * 100), (int)(height * 100));
                    var ratioWidth = (int)(width * 100) / gcd;
                    var ratioHeight = (int)(height * 100) / gcd;

                    // 显示常见比例的友好名称
                    string ratioText;
                    if (Math.Abs(ratio - 4f / 3f) < 0.1f)
                    {
                        ratioText = "4:3 (标准)";
                    }
                    else if (Math.Abs(ratio - 16f / 9f) < 0.1f)
                    {
                        ratioText = "16:9 (宽屏)";
                    }
                    else if (Math.Abs(ratio - 16f / 10f) < 0.1f)
                    {
                        ratioText = "16:10 (宽屏)";
                    }
                    else if (Math.Abs(ratio - 1f) < 0.1f)
                    {
                        ratioText = "1:1 (正方形)";
                    }
                    else
                    {
                        // 简化比例显示
                        var simpleRatioWidth = Math.Round(ratio, 1);
                        ratioText = $"{simpleRatioWidth}:1";
                    }

                    lblRatioValue.Text = ratioText;
                }
            }
            catch (Exception ex)
            {
                lblRatioValue.Text = "计算错误";
                Console.WriteLine($"更新宽高比显示时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 计算最大公约数
        /// </summary>
        private static int CalculateGCD(int a, int b)
        {
            while (b != 0)
            {
                int temp = b;
                b = a % b;
                a = temp;
            }
            return a;
        }





        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                // 保存设置
                SaveSettings();
                DialogResult = DialogResult.OK;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件 - 不保存配置直接关闭窗口
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        /// <summary>
        /// 窗体关闭事件 - 确保在直接关闭窗口时自动保存配置
        /// </summary>
        private void PageSetupForm_FormClosing(object? sender, FormClosingEventArgs e)
        {
            // 只有在直接关闭窗口（不是通过按钮）且不是取消时才保存配置
            // 避免与按钮点击事件中的保存操作重复
            if (DialogResult == DialogResult.None)
            {
                try
                {
                    SaveSettings();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"保存页面设置失败: {ex.Message}", "错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }



        /// <summary>
        /// 保存设置
        /// </summary>
        private void SaveSettings()
        {
            try
            {
                // 更新当前设置
                _currentSettings.Width = (float)numWidth.Value;
                _currentSettings.Height = (float)numHeight.Value;
                _currentSettings.Unit = comboWidthUnit.SelectedItem?.ToString() ?? "厘米";
                _currentSettings.IsLandscape = radioLandscape.Checked;
                _currentSettings.MaintainAspectRatio = chkMaintainRatio.Checked;

                // 保存复选框状态
                _currentSettings.EnablePresetSizes = chkEnablePresetSizes.Checked;
                _currentSettings.EnableSizeType = chkEnableSizeType.Checked;
                _currentSettings.EnableCustomSize = chkEnableCustomSize.Checked;
                _currentSettings.EnableAspectRatioAdjustment = chkEnableAspectRatio.Checked;

                // 确定尺寸类型
                if (radioStandard.Checked)
                    _currentSettings.SizeType = "Standard";
                else if (radioWidescreen.Checked)
                    _currentSettings.SizeType = "Widescreen";
                else
                    _currentSettings.SizeType = "Custom";

                // 更新背景设置
                _currentSettings.BackgroundSettings = _backgroundSettings;

                // 直接保存页面设置，避免通过UpdateConfig导致的复杂调用链
                ConfigService.Instance.SavePageSetupSettings(_currentSettings);

                // 记录保存成功的日志
                LogService.Instance.LogConfigChange($"页面设置已保存: {_currentSettings.GetSizeDescription()}");
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"保存页面设置失败: {ex.Message}", ex);
                throw; // 重新抛出异常，让调用者处理
            }
        }

        /// <summary>
        /// 获取当前页面设置
        /// </summary>
        public PageSetupSettings GetCurrentSettings()
        {
            // 只更新当前设置对象，不保存到配置文件
            // 配置文件的保存已经在确定按钮点击事件中处理
            _currentSettings.Width = (float)numWidth.Value;
            _currentSettings.Height = (float)numHeight.Value;
            _currentSettings.Unit = comboWidthUnit.SelectedItem?.ToString() ?? "厘米";
            _currentSettings.IsLandscape = radioLandscape.Checked;
            _currentSettings.MaintainAspectRatio = chkMaintainRatio.Checked;

            // 更新复选框状态
            _currentSettings.EnablePresetSizes = chkEnablePresetSizes.Checked;
            _currentSettings.EnableSizeType = chkEnableSizeType.Checked;
            _currentSettings.EnableCustomSize = chkEnableCustomSize.Checked;
            _currentSettings.EnableAspectRatioAdjustment = chkEnableAspectRatio.Checked;

            // 确定尺寸类型
            if (radioStandard.Checked)
                _currentSettings.SizeType = "Standard";
            else if (radioWidescreen.Checked)
                _currentSettings.SizeType = "Widescreen";
            else
                _currentSettings.SizeType = "Custom";

            // 更新背景设置
            _currentSettings.BackgroundSettings = _backgroundSettings;

            return _currentSettings;
        }

        /// <summary>
        /// 初始化背景设置控件
        /// </summary>
        private void InitializeBackgroundControls()
        {
            // 初始化图片填充模式下拉框
            comboImageFillMode.Items.Clear();
            comboImageFillMode.Items.AddRange(new string[]
            {
                "拉伸",
                "平铺",
                "居中",
                "适应",
                "填充"
            });
            comboImageFillMode.SelectedIndex = 0;

            // 设置默认状态
            radioNoBackground.Checked = true;
            UpdateBackgroundControlsState();
        }

        /// <summary>
        /// 更新背景控件状态
        /// </summary>
        private void UpdateBackgroundControlsState()
        {
            var isSolidColor = radioSolidColor.Checked;
            var isGradient = radioGradient.Checked;
            var isImage = radioImage.Checked;

            // 纯色相关控件
            panelColorPicker.Enabled = isSolidColor;
            btnSelectColor.Enabled = isSolidColor;
            lblBackgroundColor.Enabled = isSolidColor;

            // 渐变相关控件
            panelGradientStart.Enabled = isGradient;
            panelGradientEnd.Enabled = isGradient;
            btnSelectGradientStart.Enabled = isGradient;
            btnSelectGradientEnd.Enabled = isGradient;
            lblGradientStart.Enabled = isGradient;
            lblGradientEnd.Enabled = isGradient;

            // 图片相关控件
            btnSelectImage.Enabled = isImage;
            lblImagePath.Enabled = isImage;
            comboImageFillMode.Enabled = isImage;
            lblImageFillMode.Enabled = isImage;
        }

        /// <summary>
        /// 背景类型选择变更事件
        /// </summary>
        private void RadioBackground_CheckedChanged(object? sender, EventArgs e)
        {
            if (_isUpdating) return;

            var radioButton = sender as RadioButton;
            if (radioButton?.Checked == true)
            {
                UpdateBackgroundControlsState();

                // 更新背景设置
                if (radioButton == radioNoBackground)
                    _backgroundSettings.BackgroundType = "None";
                else if (radioButton == radioSolidColor)
                    _backgroundSettings.BackgroundType = "SolidColor";
                else if (radioButton == radioGradient)
                    _backgroundSettings.BackgroundType = "Gradient";
                else if (radioButton == radioImage)
                    _backgroundSettings.BackgroundType = "Image";
            }
        }

        /// <summary>
        /// 颜色面板点击事件
        /// </summary>
        private void PanelColorPicker_Click(object? sender, EventArgs e)
        {
            BtnSelectColor_Click(sender, e);
        }

        /// <summary>
        /// 选择颜色按钮点击事件
        /// </summary>
        private void BtnSelectColor_Click(object? sender, EventArgs e)
        {
            try
            {
                using var colorDialog = new ColorDialog();
                colorDialog.Color = panelColorPicker.BackColor;
                colorDialog.FullOpen = true;

                if (colorDialog.ShowDialog(this) == DialogResult.OK)
                {
                    panelColorPicker.BackColor = colorDialog.Color;
                    _backgroundSettings.BackgroundColor = ColorTranslator.ToHtml(colorDialog.Color);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择颜色时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 选择图片按钮点击事件
        /// </summary>
        private void BtnSelectImage_Click(object? sender, EventArgs e)
        {
            try
            {
                using var openFileDialog = new OpenFileDialog();
                openFileDialog.Title = "选择背景图片";
                openFileDialog.Filter = "图片文件|*.jpg;*.jpeg;*.png;*.bmp;*.gif|所有文件|*.*";
                openFileDialog.FilterIndex = 1;

                if (openFileDialog.ShowDialog(this) == DialogResult.OK)
                {
                    _backgroundSettings.ImagePath = openFileDialog.FileName;

                    // 显示选择的文件名（不显示完整路径）
                    var fileName = Path.GetFileName(openFileDialog.FileName);
                    btnSelectImage.Text = $"已选择: {fileName}";

                    // 设置工具提示显示完整路径
                    var toolTip = new ToolTip();
                    toolTip.SetToolTip(btnSelectImage, openFileDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择图片时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 渐变起始颜色面板点击事件
        /// </summary>
        private void PanelGradientStart_Click(object? sender, EventArgs e)
        {
            BtnSelectGradientStart_Click(sender, e);
        }

        /// <summary>
        /// 渐变结束颜色面板点击事件
        /// </summary>
        private void PanelGradientEnd_Click(object? sender, EventArgs e)
        {
            BtnSelectGradientEnd_Click(sender, e);
        }

        /// <summary>
        /// 选择渐变起始颜色按钮点击事件
        /// </summary>
        private void BtnSelectGradientStart_Click(object? sender, EventArgs e)
        {
            try
            {
                using var colorDialog = new ColorDialog();
                colorDialog.Color = panelGradientStart.BackColor;
                colorDialog.FullOpen = true;

                if (colorDialog.ShowDialog(this) == DialogResult.OK)
                {
                    panelGradientStart.BackColor = colorDialog.Color;
                    _backgroundSettings.GradientStartColor = ColorTranslator.ToHtml(colorDialog.Color);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择渐变起始颜色时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 选择渐变结束颜色按钮点击事件
        /// </summary>
        private void BtnSelectGradientEnd_Click(object? sender, EventArgs e)
        {
            try
            {
                using var colorDialog = new ColorDialog();
                colorDialog.Color = panelGradientEnd.BackColor;
                colorDialog.FullOpen = true;

                if (colorDialog.ShowDialog(this) == DialogResult.OK)
                {
                    panelGradientEnd.BackColor = colorDialog.Color;
                    _backgroundSettings.GradientEndColor = ColorTranslator.ToHtml(colorDialog.Color);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择渐变结束颜色时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 宽度单位变更事件 - 处理宽度单位转换，确保数值正确显示
        /// </summary>
        private void ComboWidthUnit_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (_isUpdating || comboWidthUnit.SelectedItem == null) return;

            var newUnit = comboWidthUnit.SelectedItem.ToString();
            var oldUnit = _currentSettings.Unit;

            if (newUnit != null && oldUnit != null && newUnit != oldUnit)
            {
                _isUpdating = true;
                try
                {
                    // 转换宽度数值到新单位
                    var oldConversion = _unitConversions[oldUnit];
                    var newConversion = _unitConversions[newUnit];
                    var conversionFactor = oldConversion / newConversion;

                    numWidth.Value *= (decimal)conversionFactor;

                    // 同步高度单位
                    comboHeightUnit.SelectedItem = newUnit;
                    numHeight.Value *= (decimal)conversionFactor;

                    _currentSettings.Unit = newUnit;
                }
                finally
                {
                    _isUpdating = false;
                }
            }

            UpdateAspectRatioDisplay();
        }

        /// <summary>
        /// 高度单位变更事件 - 处理高度单位转换，确保数值正确显示
        /// </summary>
        private void ComboHeightUnit_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (_isUpdating || comboHeightUnit.SelectedItem == null) return;

            var newUnit = comboHeightUnit.SelectedItem.ToString();
            var oldUnit = _currentSettings.Unit;

            if (newUnit != null && oldUnit != null && newUnit != oldUnit)
            {
                _isUpdating = true;
                try
                {
                    // 转换高度数值到新单位
                    var oldConversion = _unitConversions[oldUnit];
                    var newConversion = _unitConversions[newUnit];
                    var conversionFactor = oldConversion / newConversion;

                    numHeight.Value *= (decimal)conversionFactor;

                    // 同步宽度单位
                    comboWidthUnit.SelectedItem = newUnit;
                    numWidth.Value *= (decimal)conversionFactor;

                    _currentSettings.Unit = newUnit;
                }
                finally
                {
                    _isUpdating = false;
                }
            }

            UpdateAspectRatioDisplay();
        }

        /// <summary>
        /// 图片填充模式下拉框变更事件
        /// </summary>
        private void ComboImageFillMode_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (_isUpdating || comboImageFillMode.SelectedItem == null) return;

            var fillMode = comboImageFillMode.SelectedItem.ToString();
            if (!string.IsNullOrEmpty(fillMode))
            {
                // 将中文填充模式转换为英文标准值
                _backgroundSettings.ImageFillMode = fillMode switch
                {
                    "拉伸" => "Stretch",
                    "平铺" => "Tile",
                    "居中" => "Center",
                    "适应" => "Fit",
                    "填充" => "Fill",
                    _ => "Stretch"
                };
            }
        }

        /// <summary>
        /// 设置控件文字居中对齐
        /// </summary>
        private void SetControlsTextAlignment()
        {
            // 设置数值框文字居中
            numWidth.TextAlign = HorizontalAlignment.Center;
            numHeight.TextAlign = HorizontalAlignment.Center;

            // 设置下拉框文字居中（通过自定义绘制）
            SetComboBoxTextCenter(comboPresetSizes);
            SetComboBoxTextCenter(comboWidthUnit);
            SetComboBoxTextCenter(comboHeightUnit);
            SetComboBoxTextCenter(comboImageFillMode);
        }

        /// <summary>
        /// 设置下拉框文字居中
        /// </summary>
        private static void SetComboBoxTextCenter(ComboBox comboBox)
        {
            comboBox.DrawMode = DrawMode.OwnerDrawFixed;
            comboBox.DrawItem += (sender, e) =>
            {
                if (e.Index < 0) return;

                e.DrawBackground();

                var text = comboBox.Items[e.Index].ToString();
                if (!string.IsNullOrEmpty(text))
                {
                    var textBounds = e.Bounds;
                    var textFlags = TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter;

                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor, textFlags);
                }

                e.DrawFocusRectangle();
            };
        }
    }
}
